<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('simple_estimate_line_items', function (Blueprint $table) {
            $table->string('discount_computation')->default('percentage')->after('discounts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('simple_estimate_line_items', function (Blueprint $table) {
            $table->dropColumn('discount_computation');
        });
    }
};
