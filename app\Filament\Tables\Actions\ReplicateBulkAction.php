<?php

namespace App\Filament\Tables\Actions;

use Closure;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Support\Facades\DB;

class ReplicateBulkAction extends BulkAction
{
    protected array $excludedAttributes = [];
    protected array $replicatedRelationships = [];
    protected array $excludedRelationshipAttributes = [];
    protected ?Closure $beforeReplicaSaved = null;
    protected ?Closure $afterReplicaSaved = null;
    protected bool $withDatabaseTransaction = false;

    public static function getDefaultName(): ?string
    {
        return 'replicate';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('filament-actions::replicate.single.label'));

        $this->modalHeading(fn(): string => __('filament-actions::replicate.single.modal.heading', ['label' => $this->getPluralModelLabel()]));

        $this->modalSubmitActionLabel(__('filament-actions::replicate.single.modal.actions.replicate.label'));

        $this->successNotificationTitle(__('filament-actions::replicate.single.notifications.replicated.title'));

        $this->color('gray');

        $this->icon('heroicon-m-square-2-stack');

        $this->requiresConfirmation();

        $this->modalIcon('heroicon-o-square-2-stack');

        $this->action(function (Collection $records): void {
            $this->process(function () use ($records): void {
                $this->replicateRecords($records);
            });
        });
    }

    public function excludeAttributes(array $attributes): static
    {
        $this->excludedAttributes = $attributes;

        return $this;
    }

    public function withReplicatedRelationships(array $relationships): static
    {
        $this->replicatedRelationships = $relationships;

        return $this;
    }

    public function withExcludedRelationshipAttributes(string $relationship, array $attributes): static
    {
        $this->excludedRelationshipAttributes[$relationship] = $attributes;

        return $this;
    }

    public function beforeReplicaSaved(Closure $callback): static
    {
        $this->beforeReplicaSaved = $callback;

        return $this;
    }

    public function afterReplicaSaved(Closure $callback): static
    {
        $this->afterReplicaSaved = $callback;

        return $this;
    }

    public function databaseTransaction(Closure|bool $condition = true): static
    {
        $this->withDatabaseTransaction = $this->evaluate($condition);

        return $this;
    }

    protected function replicateRecords(Collection $records): void
    {
        $operation = function () use ($records) {
            foreach ($records as $record) {
                $this->replicateRecord($record);
            }
        };

        if ($this->withDatabaseTransaction) {
            DB::transaction($operation);
        } else {
            $operation();
        }
    }

    protected function replicateRecord(Model $record): Model
    {
        $replica = $record->replicate($this->excludedAttributes);

        if ($this->beforeReplicaSaved) {
            $this->evaluate($this->beforeReplicaSaved, [
                'replica' => $replica,
                'record' => $record,
            ]);
        }

        $replica->save();

        $this->replicateRelationships($record, $replica);

        if ($this->afterReplicaSaved) {
            $this->evaluate($this->afterReplicaSaved, [
                'replica' => $replica,
                'record' => $record,
            ]);
        }

        return $replica;
    }

    protected function replicateRelationships(Model $original, Model $replica): void
    {
        foreach ($this->replicatedRelationships as $relationshipName) {
            $relationship = $original->{$relationshipName}();
            $relatedRecords = $relationship->get();

            foreach ($relatedRecords as $relatedRecord) {
                $excludedAttributes = $this->excludedRelationshipAttributes[$relationshipName] ?? [];
                $replicatedRelated = $relatedRecord->replicate($excludedAttributes);

                // Set the foreign key to point to the replica
                $foreignKey = $relationship->getForeignKeyName();
                $replicatedRelated->{$foreignKey} = $replica->getKey();

                $replicatedRelated->save();
            }
        }
    }
}
