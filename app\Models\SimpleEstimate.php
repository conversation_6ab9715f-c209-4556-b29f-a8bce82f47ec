<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Enums\EstimateStatus;
use App\Enums\PaymentTerms;
use App\Enums\DocumentDiscountMethod;
use App\Enums\AdjustmentComputation;
use App\Traits\Blamable;
use App\Traits\CompanyOwned;

class SimpleEstimate extends Model
{
    use HasFactory, Blamable, CompanyOwned;

    protected $fillable = [
        'company_id',
        'client_id',
        'estimate_number',
        'reference_number',
        'date',
        'expiration_date',
        'payment_terms',
        'status',
        'currency_code',
        'discount_method',
        'discount_computation',
        'discount_rate',
        'subtotal',
        'tax_total',
        'discount_total',
        'total',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date' => 'date',
        'expiration_date' => 'date',
        'payment_terms' => PaymentTerms::class,
        'status' => EstimateStatus::class,
        'discount_method' => DocumentDiscountMethod::class,
        'discount_computation' => AdjustmentComputation::class,
        'discount_rate' => 'decimal:2',
        'subtotal' => 'integer',
        'tax_total' => 'integer',
        'discount_total' => 'integer',
        'total' => 'integer',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(SimpleEstimateLineItem::class, 'simple_estimate_id');
    }

    public static function getNextDocumentNumber(): string
    {
        $lastEstimate = static::orderBy('id', 'desc')->first();
        $lastNumber = $lastEstimate ? (int) substr($lastEstimate->estimate_number, 3) : 0;

        return 'EST-' . str_pad($lastNumber + 1, 6, '0', STR_PAD_LEFT);
    }
}
