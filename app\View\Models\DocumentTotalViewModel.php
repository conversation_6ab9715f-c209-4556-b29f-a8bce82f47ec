<?php

namespace App\View\Models;

use App\Enums\AdjustmentComputation;
use App\Enums\DocumentDiscountMethod;
use App\Enums\DocumentType;
use App\Models\Adjustment;
use App\Services\NumberFormattingService;
use App\Utilities\CurrencyAccessor;
use App\Utilities\CurrencyConverter;
use App\Utilities\RateCalculator;

class DocumentTotalViewModel
{
    public function __construct(
        public ?array $data,
        public DocumentType $documentType = DocumentType::Invoice,
    ) {}

    public function buildViewData(): array
    {
        if (!is_array($this->data)) {
            $this->data = [];
        }

        $currencyCode = CurrencyAccessor::getDefaultCurrency();

        $lineItems = collect($this->data['lineItems'] ?? []);

        $subtotal = $lineItems->sum(fn($item) => $this->calculateLineSubtotal($item, $currencyCode));

        $taxTotal = $this->calculateTaxTotal($lineItems, $currencyCode);
        $discountTotal = $this->calculateDiscountTotal($lineItems, $subtotal, $currencyCode);

        $grandTotal = $subtotal + ($taxTotal - $discountTotal);

        $amountDue = $this->calculateAmountDue($grandTotal);

        $discountMethod = DocumentDiscountMethod::parse($this->data['discount_method'] ?? null);
        $isPerDocumentDiscount = $discountMethod?->isPerDocument() ?? false;

        $taxTotalFormatted = $taxTotal > 0
            ? NumberFormattingService::formatMoney($taxTotal, $currencyCode) : null;

        $discountTotalFormatted = ($isPerDocumentDiscount || $discountTotal > 0)
            ? NumberFormattingService::formatMoney($discountTotal, $currencyCode) : null;

        $hasLineItems = !empty($this->data['lineItems']) && count($this->data['lineItems']) > 0;
        $subtotalFormatted = ($taxTotalFormatted || $discountTotalFormatted || $hasLineItems)
            ? NumberFormattingService::formatMoney($subtotal, $currencyCode) : null;

        $grandTotalFormatted = NumberFormattingService::formatMoney($grandTotal, $currencyCode);

        $amountDueFormatted = $this->documentType !== DocumentType::Estimate
            ? NumberFormattingService::formatMoney($amountDue, $currencyCode) : null;

        return [
            'subtotal' => $subtotalFormatted,
            'taxTotal' => $taxTotalFormatted,
            'discountTotal' => $discountTotalFormatted,
            'grandTotal' => $grandTotalFormatted,
            'amountDue' => $amountDueFormatted,
            'currencyCode' => $currencyCode,
            'isPerDocumentDiscount' => $isPerDocumentDiscount,
        ];
    }

    private function calculateLineSubtotal(array $item, string $currencyCode): float
    {
        // Handle both decimal input (from forms) and bigInteger (from database)
        $quantity = $this->convertToFloat($item['quantity'] ?? 0);
        $unitPrice = $this->convertToFloat($item['unit_price'] ?? 0, $currencyCode);

        return $quantity * $unitPrice;
    }

    private function convertToFloat($value, ?string $currencyCode = null): float
    {
        if (is_string($value) && $currencyCode && CurrencyConverter::isValidAmount($value, $currencyCode)) {
            return CurrencyConverter::convertToFloat($value, $currencyCode);
        }

        // If it's a large integer (likely from bigInteger storage), convert from cents
        if (is_numeric($value) && $value > 1000) {
            return (float) $value / 100;
        }

        return max((float) $value, 0);
    }

    private function calculateTaxTotal($lineItems, string $currencyCode): float
    {
        return $lineItems->reduce(function ($carry, $item) use ($currencyCode) {
            $quantity = $this->convertToFloat($item['quantity'] ?? 0);
            $unitPrice = $this->convertToFloat($item['unit_price'] ?? 0, $currencyCode);

            $lineTotal = $quantity * $unitPrice;

            // Check if manual tax input is provided
            if (!empty($item['taxes'])) {
                $taxValue = (float) $item['taxes'];
                // Assume it's a percentage if it's a reasonable percentage value
                if ($taxValue <= 100) {
                    $taxAmount = $lineTotal * ($taxValue / 100);
                } else {
                    // Treat as fixed amount
                    $taxAmount = $taxValue;
                }
                return $carry + $taxAmount;
            }

            // Fallback to original tax_rate calculation
            $taxRate = (float) ($item['tax_rate'] ?? 0);
            $taxAmount = $lineTotal * ($taxRate / 100);

            return $carry + $taxAmount;
        }, 0.0);
    }

    private function calculateAdjustmentsTotal($lineItems, string $key, string $currencyCode): float
    {
        return $lineItems->reduce(function ($carry, $item) use ($key, $currencyCode) {
            $quantity = $this->convertToFloat($item['quantity'] ?? 0);
            $unitPrice = $this->convertToFloat($item['unit_price'] ?? 0, $currencyCode);

            $adjustmentIds = $item[$key] ?? [];
            $lineTotal = $quantity * $unitPrice;

            $adjustmentTotal = Adjustment::whereIn('id', $adjustmentIds)
                ->get()
                ->sum(function (Adjustment $adjustment) use ($lineTotal) {
                    if ($adjustment->computation->isPercentage()) {
                        return RateCalculator::calculatePercentage($lineTotal, $adjustment->getRawOriginal('rate'));
                    } else {
                        return $adjustment->getRawOriginal('rate');
                    }
                });

            return $carry + $adjustmentTotal;
        }, 0.0);
    }

    private function calculateDiscountTotal($lineItems, float $subtotal, string $currencyCode): float
    {
        // Protection contre les données nulles
        if (!is_array($this->data)) {
            return 0.0;
        }

        $discountMethod = DocumentDiscountMethod::parse($this->data['discount_method'] ?? null) ?? DocumentDiscountMethod::PerLineItem;

        if ($discountMethod->isPerLineItem()) {
            // Check for manual discount inputs in line items
            $manualDiscountTotal = $this->calculateManualDiscountTotal($lineItems, $currencyCode);
            if ($manualDiscountTotal > 0) {
                return $manualDiscountTotal;
            }
            return $this->calculateAdjustmentsTotal($lineItems, 'salesDiscounts', $currencyCode);
        }

        $discountComputation = AdjustmentComputation::parse($this->data['discount_computation'] ?? null) ?? AdjustmentComputation::Percentage;
        $discountRate = blank($this->data['discount_rate'] ?? null) ? '0' : $this->data['discount_rate'];

        if ($discountComputation->isPercentage()) {
            $scaledDiscountRate = RateCalculator::parseLocalizedRate($discountRate);

            return RateCalculator::calculatePercentage($subtotal, $scaledDiscountRate);
        }

        if (!CurrencyConverter::isValidAmount($discountRate, $currencyCode)) {
            $discountRate = '0';
        }

        return CurrencyConverter::convertToFloat($discountRate, $currencyCode);
    }

    private function calculateManualDiscountTotal($lineItems, string $currencyCode): float
    {
        return $lineItems->reduce(function ($carry, $item) use ($currencyCode) {
            $quantity = $this->convertToFloat($item['quantity'] ?? 0);
            $unitPrice = $this->convertToFloat($item['unit_price'] ?? 0, $currencyCode);

            $lineTotal = $quantity * $unitPrice;

            // Check for SimpleInvoice/SimpleEstimate discount_rate field
            if (!empty($item['discount_rate'])) {
                $discountValue = (float) $item['discount_rate'];
                $discountComputation = AdjustmentComputation::parse($item['discount_computation'] ?? null) ?? AdjustmentComputation::Percentage;

                if ($discountComputation->isPercentage()) {
                    $discountAmount = $lineTotal * ($discountValue / 100);
                } else {
                    $discountAmount = $discountValue; // Fixed amount
                }
                return $carry + $discountAmount;
            }

            // Check for legacy discounts field (SimpleEstimate)
            if (!empty($item['discounts'])) {
                $discountValue = (float) $item['discounts'];
                $discountComputation = AdjustmentComputation::parse($item['discount_computation'] ?? null) ?? AdjustmentComputation::Percentage;

                if ($discountComputation->isPercentage()) {
                    $discountAmount = $lineTotal * ($discountValue / 100);
                } else {
                    $discountAmount = $discountValue; // Fixed amount
                }
                return $carry + $discountAmount;
            }

            return $carry;
        }, 0.0);
    }

    private function calculateAmountDue(float $grandTotal): float
    {
        $amountPaid = (float) ($this->data['amount_paid'] ?? 0);

        return $grandTotal - $amountPaid;
    }
}
