<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('simple_estimates', function (Blueprint $table) {
            $table->date('expiration_date')->nullable()->after('date');
            $table->string('payment_terms')->default('due_upon_receipt')->after('expiration_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('simple_estimates', function (Blueprint $table) {
            $table->dropColumn(['expiration_date', 'payment_terms']);
        });
    }
};
