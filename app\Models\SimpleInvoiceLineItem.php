<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Enums\OfferingType;
use App\Enums\AdjustmentComputation;

class SimpleInvoiceLineItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'simple_invoice_id',
        'line_number',
        'offering_name',
        'type',
        'description',
        'quantity',
        'unit_price',
        'tax_rate',
        'discount_rate',
        'discount_computation',
        'taxes',
        'discounts',
        'line_total',
        'total_excl_tax',
        'total_incl_tax',
    ];

    protected $casts = [
        'line_number' => 'integer',
        'type' => OfferingType::class,
        'quantity' => 'integer',
        'unit_price' => 'integer',
        'tax_rate' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'discount_computation' => AdjustmentComputation::class,
        'line_total' => 'integer',
        'total_excl_tax' => 'decimal:2',
        'total_incl_tax' => 'decimal:2',
    ];

    public function simpleInvoice(): BelongsTo
    {
        return $this->belongsTo(SimpleInvoice::class);
    }
}
