<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('simple_invoice_line_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('simple_invoice_id')->constrained()->cascadeOnDelete();
            $table->integer('line_number')->default(1);
            $table->string('offering_name');
            $table->string('type')->default('product'); // product or service
            $table->text('description')->nullable();
            $table->bigInteger('quantity')->default(1);
            $table->bigInteger('unit_price')->default(0);
            $table->string('taxes')->nullable();
            $table->string('discounts')->nullable();
            $table->bigInteger('line_total')->default(0);
            $table->timestamps();

            $table->index(['simple_invoice_id', 'line_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('simple_invoice_line_items');
    }
};
