<?php

namespace App\Models;

use App\Casts\MoneyCast;
use App\Casts\RateCast;
use App\Enums\AdjustmentComputation;
use App\Enums\DocumentDiscountMethod;
use App\Enums\DocumentType;
use App\Enums\InvoiceStatus;
use App\Enums\PaymentTerms;
use App\Enums\TransactionType;
use App\Filament\Company\Resources\InvoiceResource;
use App\Models\Client;
use App\Models\Company;
use App\Models\DocumentDefault;
use Filament\Actions\Action;
use Filament\Actions\MountableAction;
use Filament\Actions\ReplicateAction;
use Filament\Actions\StaticAction;
use Filament\Notifications\Notification;
use Filament\Support\Enums\Alignment;
use Illuminate\Database\Eloquent\Attributes\CollectedBy;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;
use Livewire\Component;

class Invoice extends Document
{
    protected $table = 'invoices';

    protected $fillable = [
        'company_id',
        'client_id',
        'logo',
        'header',
        'subheader',
        'invoice_number',
        'order_number',
        'date',
        'due_date',
        'payment_terms',
        'approved_at',
        'last_sent_at',
        'paid_at',
        'last_viewed_at',
        'status',
        'currency_code',
        'discount_method',
        'discount_computation',
        'discount_rate',
        'subtotal',
        'tax_total',
        'discount_total',
        'total',
        'amount_paid',
        'amount_due',
        'notes',
        'footer',
        'viewed_by',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date' => 'date',
        'due_date' => 'date',
        'payment_terms' => PaymentTerms::class,
        'approved_at' => 'datetime',
        'last_sent_at' => 'datetime',
        'paid_at' => 'datetime',
        'last_viewed_at' => 'datetime',
        'status' => InvoiceStatus::class,
        'discount_method' => DocumentDiscountMethod::class,
        'discount_computation' => AdjustmentComputation::class,
        'discount_rate' => RateCast::class,
        'subtotal' => 'decimal:2',
        'tax_total' => 'decimal:2',
        'discount_total' => 'decimal:2',
        'total' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'amount_due' => 'decimal:2',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function estimate(): BelongsTo
    {
        return $this->belongsTo(Estimate::class);
    }

    public function transactions(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }

    public function lineItems(): MorphMany
    {
        return $this->morphMany(DocumentLineItem::class, 'documentable');
    }

    public function payments(): MorphMany
    {
        return $this->transactions()->where('is_payment', true);
    }

    public function deposits(): MorphMany
    {
        return $this->transactions()->where('type', TransactionType::Deposit)->where('is_payment', true);
    }

    public static function documentType(): DocumentType
    {
        return DocumentType::Invoice;
    }

    public function documentNumber(): ?string
    {
        return $this->invoice_number;
    }

    public function documentDate(): ?string
    {
        return $this->date?->toDefaultDateFormat();
    }

    public function dueDate(): ?string
    {
        return $this->due_date?->toDefaultDateFormat();
    }

    public function referenceNumber(): ?string
    {
        return $this->order_number;
    }

    public function amountDue(): ?string
    {
        return $this->amount_due ? (string) $this->amount_due : null;
    }

    public function scopeUnpaid(Builder $query): Builder
    {
        return $query->whereNotIn('status', [
            InvoiceStatus::Paid,
            InvoiceStatus::Void,
            InvoiceStatus::Draft,
            InvoiceStatus::Overpaid,
        ]);
    }

    public function scopeOverdue(Builder $query): Builder
    {
        return $query
            ->unpaid()
            ->where('status', InvoiceStatus::Overdue);
    }

    protected function isCurrentlyOverdue(): Attribute
    {
        return Attribute::get(function () {
            return $this->due_date->isBefore(today()) && $this->canBeOverdue();
        });
    }

    public function isDraft(): bool
    {
        return $this->status === InvoiceStatus::Draft;
    }

    public function wasApproved(): bool
    {
        return $this->approved_at !== null;
    }

    public function isPaid(): bool
    {
        return $this->paid_at !== null;
    }

    public function hasBeenSent(): bool
    {
        return $this->last_sent_at !== null;
    }

    public function hasBeenViewed(): bool
    {
        return $this->last_viewed_at !== null;
    }

    public function canRecordPayment(): bool
    {
        return !in_array($this->status, [
            InvoiceStatus::Draft,
            InvoiceStatus::Paid,
            InvoiceStatus::Void,
        ]);
    }

    public function canBulkRecordPayment(): bool
    {
        return !in_array($this->status, [
            InvoiceStatus::Draft,
            InvoiceStatus::Paid,
            InvoiceStatus::Void,
            InvoiceStatus::Overpaid,
        ]);
    }

    public function canBeOverdue(): bool
    {
        return in_array($this->status, InvoiceStatus::canBeOverdue());
    }

    public function canBeApproved(): bool
    {
        return $this->isDraft() && !$this->wasApproved();
    }

    public function canBeMarkedAsSent(): bool
    {
        return !$this->hasBeenSent();
    }

    public function hasPayments(): bool
    {
        return $this->payments()->exists();
    }

    public static function getNextDocumentNumber(?Company $company = null): string
    {
        $company ??= auth()->user()?->currentCompany;

        if (!$company) {
            throw new \RuntimeException('No current company is set for the user.');
        }

        $defaultInvoiceSettings = $company->defaultInvoice;

        $numberPrefix = $defaultInvoiceSettings?->number_prefix ?? '';

        $latestDocument = static::query()
            ->whereNotNull('invoice_number')
            ->latest('invoice_number')
            ->first();

        $lastNumberNumericPart = $latestDocument
            ? (int) substr($latestDocument->invoice_number, strlen($numberPrefix))
            : DocumentDefault::getBaseNumber();

        $numberNext = $lastNumberNumericPart + 1;

        if ($defaultInvoiceSettings) {
            return $defaultInvoiceSettings->getNumberNext(
                prefix: $numberPrefix,
                next: $numberNext
            );
        }

        return $numberPrefix . $numberNext;
    }

    public static function getBlockedApproveAction(string $action = Action::class): MountableAction
    {
        return $action::make('blockedApprove')
            ->label('Approve')
            ->icon('heroicon-m-check-circle')
            ->visible(fn(self $record) => $record->canBeApproved() && $record->hasInactiveAdjustments())
            ->requiresConfirmation()
            ->modalAlignment(Alignment::Start)
            ->modalIconColor('danger')
            ->modalDescription(function (self $record) {
                $inactiveAdjustments = collect();

                foreach ($record->lineItems as $lineItem) {
                    foreach ($lineItem->adjustments as $adjustment) {
                        if ($adjustment->isInactive() && $inactiveAdjustments->doesntContain($adjustment->name)) {
                            $inactiveAdjustments->push($adjustment->name);
                        }
                    }
                }

                $output = "<p class='text-sm mb-4'>This invoice contains inactive adjustments that need to be addressed before approval:</p>";
                $output .= "<ul role='list' class='list-disc list-inside space-y-1 text-sm'>";

                foreach ($inactiveAdjustments as $name) {
                    $output .= "<li class='py-1'><span class='font-medium'>{$name}</span></li>";
                }

                $output .= '</ul>';
                $output .= "<p class='text-sm mt-4'>Please update these adjustments before approving the invoice.</p>";

                return new HtmlString($output);
            })
            ->modalSubmitAction(function (StaticAction $action, self $record) {
                $action->label('Edit Invoice')
                    ->url(InvoiceResource\Pages\EditInvoice::getUrl(['record' => $record->id]));
            });
    }

    public static function getApproveDraftAction(string $action = Action::class): MountableAction
    {
        return $action::make('approveDraft')
            ->label('Approve')
            ->icon('heroicon-m-check-circle')
            ->visible(function (self $record) {
                return $record->canBeApproved();
            })
            ->requiresConfirmation()
            ->databaseTransaction()
            ->successNotificationTitle('Invoice approved')
            ->action(function (self $record, MountableAction $action, Component $livewire) {
                if ($record->hasInactiveAdjustments()) {
                    $isViewPage = $livewire instanceof InvoiceResource\Pages\ViewInvoice;

                    if (!$isViewPage) {
                        redirect(InvoiceResource\Pages\ViewInvoice::getUrl(['record' => $record->id]));
                    } else {
                        Notification::make()
                            ->warning()
                            ->title('Cannot approve invoice')
                            ->body('This invoice has inactive adjustments that must be addressed first.')
                            ->persistent()
                            ->send();
                    }
                } else {
                    $record->approveDraft();

                    $action->success();
                }
            });
    }
}
