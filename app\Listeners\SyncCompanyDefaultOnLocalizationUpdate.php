<?php

namespace App\Listeners;

use App\Models\Localization;
use App\Services\CompanyDefaultSyncService;
use App\Services\CurrencyCreationService;
use Illuminate\Support\Facades\Log;

class SyncCompanyDefaultOnLocalizationUpdate
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event when Localization is created.
     */
    public function created(Localization $localization): void
    {
        $this->syncCompanyDefault($localization, 'created');
    }

    /**
     * Handle the event when Localization is updated.
     */
    public function updated(Localization $localization): void
    {
        $this->syncCompanyDefault($localization, 'updated');
    }

    /**
     * Synchronise CompanyDefault et les devises lorsqu'une localisation est modifiée
     */
    private function syncCompanyDefault(Localization $localization, string $action): void
    {
        try {
            Log::info("Synchronisation CompanyDefault et devises déclenchée par {$action} de la localisation (ID: {$localization->id}, Company: {$localization->company_id})");

            // 1. Synchroniser CompanyDefault
            $syncService = app(CompanyDefaultSyncService::class);
            $syncService->syncFromLocalization($localization);

            Log::info("Synchronisation CompanyDefault terminée pour la localisation {$localization->id}");

            // 2. Synchroniser les devises avec la nouvelle localisation
            $currencyService = app(CurrencyCreationService::class);
            $company = $localization->company;

            if ($company) {
                $currencyService->syncCompanyCurrenciesWithLocalization($company);
                Log::info("Synchronisation des devises terminée pour l'entreprise {$company->id}");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de la synchronisation pour la localisation {$localization->id}: " . $e->getMessage());
        }
    }
}
