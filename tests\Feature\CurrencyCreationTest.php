<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Company;
use App\Models\Currency;
use App\Models\CompanyProfile;
use App\Models\Localization;
use App\Models\CompanyDefault;
use App\Enums\EntityType;
use App\Enums\DateFormat;
use App\Enums\TimeFormat;
use App\Enums\WeekStart;
use App\Enums\NumberFormat;
use App\Services\CurrencyCreationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Wallo\FilamentCompanies\Events\CompanyCreated;

class CurrencyCreationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Company $company;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur de test
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_creates_default_currency_when_company_is_created()
    {
        // Créer une entreprise avec un profil et une localisation
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'personal_company' => false,
        ]);

        // Créer le profil avec une devise
        $profile = CompanyProfile::factory()->create([
            'company_id' => $company->id,
            'currency_code' => 'EUR',
            'email' => '<EMAIL>',
            'entity_type' => EntityType::Company,
            'rccm' => 'TEST123',
            'fiscal_number' => 'FISCAL123',
            'siret_number' => 'SIRET123',
        ]);

        // Créer la localisation
        $localization = Localization::factory()->create([
            'company_id' => $company->id,
            'language' => 'fr',
            'timezone' => 'Europe/Paris',
            'date_format' => DateFormat::DMY_SLASH,
            'time_format' => TimeFormat::H24,
            'fiscal_year_end_month' => 12,
            'fiscal_year_end_day' => 31,
            'week_start' => WeekStart::Monday,
            'number_format' => NumberFormat::SpaceComma,
            'percent_first' => false,
        ]);

        // Déclencher l'événement de création d'entreprise
        event(new CompanyCreated($company));

        // Vérifier qu'une devise a été créée
        $this->assertDatabaseHas('currencies', [
            'company_id' => $company->id,
            'code' => 'EUR',
            'enabled' => true,
        ]);

        // Vérifier que la devise a les bons paramètres de formatage
        $currency = Currency::where('company_id', $company->id)
            ->where('code', 'EUR')
            ->first();

        $this->assertNotNull($currency);
        $this->assertEquals('EUR', $currency->code);
        $this->assertEquals(',', $currency->decimal_mark); // SpaceComma format
        $this->assertEquals(' ', $currency->thousands_separator); // SpaceComma format
        $this->assertTrue($currency->enabled);

        // Vérifier que CompanyDefault a été créé
        $this->assertDatabaseHas('company_defaults', [
            'company_id' => $company->id,
            'currency_code' => 'EUR',
        ]);
    }

    /** @test */
    public function it_creates_currency_with_correct_localization_formatting()
    {
        $currencyService = new CurrencyCreationService();

        // Créer une entreprise avec localisation française
        $company = Company::factory()->create();
        
        $profile = CompanyProfile::factory()->create([
            'company_id' => $company->id,
            'currency_code' => 'USD',
        ]);

        $localization = Localization::factory()->create([
            'company_id' => $company->id,
            'number_format' => NumberFormat::CommaDot, // Format américain
        ]);

        // Créer la devise
        $currency = $currencyService->createDefaultCurrencyForCompany($company);

        $this->assertNotNull($currency);
        $this->assertEquals('USD', $currency->code);
        $this->assertEquals('.', $currency->decimal_mark); // CommaDot format
        $this->assertEquals(',', $currency->thousands_separator); // CommaDot format
    }

    /** @test */
    public function it_does_not_create_duplicate_currency()
    {
        $currencyService = new CurrencyCreationService();

        $company = Company::factory()->create();
        
        CompanyProfile::factory()->create([
            'company_id' => $company->id,
            'currency_code' => 'XOF',
        ]);

        Localization::factory()->create([
            'company_id' => $company->id,
        ]);

        // Créer la devise une première fois
        $currency1 = $currencyService->createDefaultCurrencyForCompany($company);
        
        // Essayer de créer la même devise une deuxième fois
        $currency2 = $currencyService->createDefaultCurrencyForCompany($company);

        $this->assertNotNull($currency1);
        $this->assertNotNull($currency2);
        $this->assertEquals($currency1->id, $currency2->id);

        // Vérifier qu'il n'y a qu'une seule devise XOF pour cette entreprise
        $currencyCount = Currency::where('company_id', $company->id)
            ->where('code', 'XOF')
            ->count();

        $this->assertEquals(1, $currencyCount);
    }

    /** @test */
    public function it_syncs_currencies_when_localization_is_updated()
    {
        $currencyService = new CurrencyCreationService();

        $company = Company::factory()->create();
        
        CompanyProfile::factory()->create([
            'company_id' => $company->id,
            'currency_code' => 'GBP',
        ]);

        $localization = Localization::factory()->create([
            'company_id' => $company->id,
            'number_format' => NumberFormat::CommaDot,
        ]);

        // Créer la devise
        $currency = $currencyService->createDefaultCurrencyForCompany($company);

        $this->assertEquals('.', $currency->decimal_mark);
        $this->assertEquals(',', $currency->thousands_separator);

        // Mettre à jour la localisation
        $localization->update([
            'number_format' => NumberFormat::DotComma,
        ]);

        // Recharger la devise
        $currency->refresh();

        // Vérifier que la devise a été mise à jour avec le nouveau formatage
        $this->assertEquals(',', $currency->decimal_mark); // DotComma format
        $this->assertEquals('.', $currency->thousands_separator); // DotComma format
    }

    /** @test */
    public function it_creates_multiple_currencies_for_company()
    {
        $currencyService = new CurrencyCreationService();

        $company = Company::factory()->create();
        
        CompanyProfile::factory()->create([
            'company_id' => $company->id,
            'currency_code' => 'EUR', // Devise par défaut
        ]);

        Localization::factory()->create([
            'company_id' => $company->id,
        ]);

        // Créer plusieurs devises
        $currencies = $currencyService->createMultipleCurrenciesForCompany($company, ['USD', 'GBP', 'XOF']);

        $this->assertCount(3, $currencies);

        // Vérifier que toutes les devises ont été créées
        foreach (['USD', 'GBP', 'XOF'] as $code) {
            $this->assertDatabaseHas('currencies', [
                'company_id' => $company->id,
                'code' => $code,
                'enabled' => true,
            ]);
        }
    }
}
