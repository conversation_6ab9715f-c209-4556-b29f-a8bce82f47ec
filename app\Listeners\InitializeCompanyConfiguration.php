<?php

namespace App\Listeners;

use App\Events\CompanyConfigured;
use App\Models\Company;
use App\Services\CompanyDefaultSyncService;
use App\Services\CurrencyCreationService;
use Illuminate\Support\Facades\Log;
use Wallo\FilamentCompanies\Events\CompanyCreated;

class InitializeCompanyConfiguration
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * Synchronise CompanyDefault et crée la devise par défaut lors de la création d'une entreprise
     */
    public function handle(CompanyCreated $event): void
    {
        try {
            /** @var Company $company */
            $company = $event->company;

            Log::info("Initialisation de la configuration pour nouvelle entreprise: {$company->name} (ID: {$company->id})");

            // 1. Synchroniser CompanyDefault
            $syncService = app(CompanyDefaultSyncService::class);
            $syncService->syncFromCompany($company);

            Log::info("Synchronisation CompanyDefault terminée pour l'entreprise {$company->id}");

            // 2. C<PERSON>er la devise par défaut dans la table currencies
            $currencyService = app(CurrencyCreationService::class);
            $defaultCurrency = $currencyService->createDefaultCurrencyForCompany($company);
            if ($defaultCurrency) {
                Log::info("Devise par défaut créée: {$defaultCurrency->code} pour l'entreprise {$company->id}");
            } else {
                Log::warning("Impossible de créer la devise par défaut pour l'entreprise {$company->id}");
            }

            // 3. Déclencher la configuration de l'entreprise
            CompanyConfigured::dispatch($company);
        } catch (\Exception $e) {
            Log::error("Erreur lors de l'initialisation de la configuration pour l'entreprise {$event->company->name}: " . $e->getMessage());
        }
    }
}
