<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('simple_estimate_line_items', function (Blueprint $table) {
            $table->decimal('total_excl_tax', 10, 2)->default(0)->after('line_total');
            $table->decimal('total_incl_tax', 10, 2)->default(0)->after('total_excl_tax');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('simple_estimate_line_items', function (Blueprint $table) {
            $table->dropColumn(['total_excl_tax', 'total_incl_tax']);
        });
    }
};
