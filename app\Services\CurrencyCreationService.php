<?php

namespace App\Services;

use App\Models\Company;
use App\Models\Currency;
use App\Models\Localization;
use App\Enums\NumberFormat;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Akaunting\Money\Currency as ISOCurrency;
use Symfony\Component\Intl\Currencies;
use Symfony\Component\Intl\Exception\MissingResourceException;

class CurrencyCreationService
{
    /**
     * Crée automatiquement la devise par défaut pour une entreprise
     * basée sur le currency_code du profil et la localisation
     */
    public function createDefaultCurrencyForCompany(Company $company): ?Currency
    {
        try {
            return DB::transaction(function () use ($company) {
                // Charger les relations nécessaires
                $company->load(['profile', 'locale']);
                
                $profile = $company->profile;
                $locale = $company->locale;
                
                if (!$profile || !$profile->currency_code) {
                    Log::warning("Aucun profil ou currency_code trouvé pour l'entreprise {$company->id}");
                    return null;
                }
                
                $currencyCode = $profile->currency_code;
                
                // Vérifier si la devise existe déjà pour cette entreprise
                $existingCurrency = Currency::where('company_id', $company->id)
                    ->where('code', $currencyCode)
                    ->first();
                
                if ($existingCurrency) {
                    Log::info("Devise {$currencyCode} existe déjà pour l'entreprise {$company->id}");
                    return $existingCurrency;
                }
                
                // Récupérer les informations de la devise depuis les sources ISO
                $currencyData = $this->getCurrencyDataFromISO($currencyCode, $locale);
                
                // Créer la devise dans la base de données
                $currency = Currency::create([
                    'company_id' => $company->id,
                    'name' => $currencyData['name'],
                    'code' => $currencyCode,
                    'precision' => $currencyData['precision'],
                    'symbol' => $currencyData['symbol'],
                    'symbol_first' => $currencyData['symbol_first'],
                    'decimal_mark' => $currencyData['decimal_mark'],
                    'thousands_separator' => $currencyData['thousands_separator'],
                    'enabled' => true,
                    'created_by' => $company->owner?->id,
                    'updated_by' => $company->owner?->id,
                ]);
                
                Log::info("Devise {$currencyCode} créée avec succès pour l'entreprise {$company->id}", [
                    'currency_id' => $currency->id,
                    'currency_name' => $currency->name,
                    'symbol' => $currency->symbol,
                ]);
                
                return $currency;
            });
        } catch (\Exception $e) {
            Log::error("Erreur lors de la création de la devise pour l'entreprise {$company->id}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Récupère les données de devise depuis les sources ISO et la localisation
     */
    private function getCurrencyDataFromISO(string $currencyCode, ?Localization $locale): array
    {
        // Données par défaut
        $defaultData = [
            'name' => $currencyCode,
            'precision' => 2,
            'symbol' => $currencyCode,
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ];
        
        try {
            // Récupérer le nom de la devise depuis Symfony Intl
            $language = $locale?->language ?? 'fr';
            $currencyName = Currencies::getName($currencyCode, $language);
            $defaultData['name'] = ucwords($currencyName);
        } catch (MissingResourceException $e) {
            Log::warning("Nom de devise non trouvé pour {$currencyCode} en {$language}");
        }
        
        try {
            // Récupérer les informations depuis Akaunting Money
            $isoCurrencies = ISOCurrency::getCurrencies();
            if (isset($isoCurrencies[$currencyCode])) {
                $isoData = $isoCurrencies[$currencyCode];
                $defaultData['precision'] = $isoData['precision'] ?? 2;
                $defaultData['symbol'] = $isoData['symbol'] ?? $currencyCode;
                $defaultData['symbol_first'] = $isoData['symbol_first'] ?? true;
            }
        } catch (\Exception $e) {
            Log::warning("Données ISO non trouvées pour {$currencyCode}: " . $e->getMessage());
        }
        
        // Appliquer le formatage basé sur la localisation de l'entreprise
        if ($locale && $locale->number_format) {
            $numberFormat = $locale->number_format;
            $defaultData['decimal_mark'] = $numberFormat->getDecimalMark();
            $defaultData['thousands_separator'] = $numberFormat->getThousandsSeparator();
        }
        
        return $defaultData;
    }
    
    /**
     * Crée plusieurs devises pour une entreprise
     */
    public function createMultipleCurrenciesForCompany(Company $company, array $currencyCodes): array
    {
        $createdCurrencies = [];
        
        foreach ($currencyCodes as $currencyCode) {
            // Temporairement définir le currency_code dans le profil
            $originalCurrencyCode = $company->profile?->currency_code;
            if ($company->profile) {
                $company->profile->currency_code = $currencyCode;
            }
            
            $currency = $this->createDefaultCurrencyForCompany($company);
            if ($currency) {
                $createdCurrencies[] = $currency;
            }
            
            // Restaurer le currency_code original
            if ($company->profile && $originalCurrencyCode) {
                $company->profile->currency_code = $originalCurrencyCode;
            }
        }
        
        return $createdCurrencies;
    }
    
    /**
     * Met à jour une devise existante avec les paramètres de localisation
     */
    public function updateCurrencyWithLocalization(Currency $currency, Localization $localization): Currency
    {
        if ($localization->number_format) {
            $currency->update([
                'decimal_mark' => $localization->number_format->getDecimalMark(),
                'thousands_separator' => $localization->number_format->getThousandsSeparator(),
                'updated_by' => auth()->id(),
            ]);
            
            Log::info("Devise {$currency->code} mise à jour avec la localisation", [
                'currency_id' => $currency->id,
                'decimal_mark' => $currency->decimal_mark,
                'thousands_separator' => $currency->thousands_separator,
            ]);
        }
        
        return $currency->fresh();
    }
    
    /**
     * Synchronise toutes les devises d'une entreprise avec sa localisation
     */
    public function syncCompanyCurrenciesWithLocalization(Company $company): void
    {
        $company->load(['currencies', 'locale']);
        
        if (!$company->locale) {
            Log::warning("Aucune localisation trouvée pour l'entreprise {$company->id}");
            return;
        }
        
        foreach ($company->currencies as $currency) {
            $this->updateCurrencyWithLocalization($currency, $company->locale);
        }
        
        Log::info("Synchronisation des devises terminée pour l'entreprise {$company->id}");
    }
}
