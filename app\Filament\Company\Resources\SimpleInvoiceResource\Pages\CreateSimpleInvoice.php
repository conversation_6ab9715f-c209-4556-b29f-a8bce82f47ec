<?php

namespace App\Filament\Company\Resources\SimpleInvoiceResource\Pages;

use App\Models\SimpleInvoice;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Company\Resources\SimpleInvoiceResource;

class CreateSimpleInvoice extends CreateRecord
{
    protected static string $resource = SimpleInvoiceResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Calculate totals based on line items and tax rate
        $lineItems = $data['lineItems'] ?? [];
        $taxRate = (float) ($data['tax_rate'] ?? 0);

        $subtotal = collect($lineItems)->sum(function ($item) {
            return ((float) ($item['quantity'] ?? 0)) * ((float) ($item['unit_price'] ?? 0));
        });

        $taxTotal = $subtotal * ($taxRate / 100);
        $total = $subtotal + $taxTotal;

        // Set calculated values
        $data['subtotal'] = $subtotal;
        $data['tax_total'] = $taxTotal;
        $data['discount_total'] = 0; // No discount in simple version
        $data['total'] = $total;
        $data['amount_due'] = $total;
        $data['amount_paid'] = 0;

        // Set default values
        $data['status'] = \App\Enums\InvoiceStatus::Draft;
        $data['discount_method'] = \App\Enums\DocumentDiscountMethod::PerDocument;

        // Remove tax_rate as it's not a field in the model
        unset($data['tax_rate']);

        return $data;
    }

    protected function afterCreate(): void
    {
        // Update line items with calculated totals
        $record = $this->record;

        foreach ($record->lineItems as $lineItem) {
            $quantity = $lineItem->quantity / 100; // Convert from cents
            $unitPrice = $lineItem->unit_price / 100; // Convert from cents
            $lineTotal = $quantity * $unitPrice * 100; // Convert back to cents

            $lineItem->update([
                'line_total' => $lineTotal,
            ]);
        }
    }
}
