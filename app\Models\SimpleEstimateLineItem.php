<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Enums\OfferingType;
use App\Enums\AdjustmentComputation;

class SimpleEstimateLineItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'simple_estimate_id',
        'line_number',
        'offering_name',
        'type',
        'description',
        'quantity',
        'unit_price',
        'taxes',
        'discounts',
        'discount_computation',
        'line_total',
        'total_excl_tax',
        'total_incl_tax',
    ];

    protected $casts = [
        'line_number' => 'integer',
        'type' => OfferingType::class,
        'quantity' => 'integer',
        'unit_price' => 'integer',
        'discount_computation' => AdjustmentComputation::class,
        'line_total' => 'integer',
        'total_excl_tax' => 'decimal:2',
        'total_incl_tax' => 'decimal:2',
    ];

    public function simpleEstimate(): BelongsTo
    {
        return $this->belongsTo(SimpleEstimate::class);
    }
}
