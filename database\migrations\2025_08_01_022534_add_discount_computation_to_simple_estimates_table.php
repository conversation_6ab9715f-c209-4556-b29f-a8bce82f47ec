<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('simple_estimates', function (Blueprint $table) {
            $table->string('discount_computation')->default('percentage')->after('discount_method');
            $table->decimal('discount_rate', 8, 2)->default(0)->after('discount_computation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('simple_estimates', function (Blueprint $table) {
            $table->dropColumn(['discount_computation', 'discount_rate']);
        });
    }
};
